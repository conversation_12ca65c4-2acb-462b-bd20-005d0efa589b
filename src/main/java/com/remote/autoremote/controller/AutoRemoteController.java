package com.remote.autoremote.controller;

import com.remote.autoremote.service.AutoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpSession;
import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Paths;

@RestController
@CrossOrigin(origins = "*")
@RequestMapping("/remote_server")
public class AutoRemoteController {

    private static final Logger log = LoggerFactory.getLogger(AutoRemoteController.class);

    @Autowired
    private AutoService autoService;

    /**
     * 判断是否有备份文件
     * @param stu_id
     * @return
     */
    @GetMapping("/checkDir")
    public ResponseEntity<Boolean> checkDir(@RequestParam("stu_id")String stu_id,@RequestParam("dir")String dir) {
        return ResponseEntity.ok(autoService.checkDirIsTrue(stu_id,dir));
    }

    /**
     * 创建一个备份文件的文件夹
     */
    @PostMapping("/createBuRecordDir")
    public ResponseEntity<String> createBuRecordDir(@RequestParam("stu_id")String stu_id,@RequestParam("dir") String dir) {
        String byRecordDir = autoService.createByRecordDir(stu_id,dir);
        return ResponseEntity.ok(byRecordDir);
    }

    /**
     *  根据id和实验名称创建自己的文件夹。
     * @param stu_id
     * @param dir
     * @return
     */
    @PostMapping("/createByUserLoginName")
    public ResponseEntity<String> createByUserLoginName(@RequestParam("stu_id") String stu_id,@RequestParam("dir") String dir) {
        String result = autoService.CopyDirByLoginName(stu_id, dir);
        return ResponseEntity.ok(result);
    }

    /**
     * 暂时废弃的方法，主要是文件拷贝
     * @param source
     * @param session
     * @return
     * @throws IOException
     */
    @PostMapping("/copy")
    public ResponseEntity<String> copy(@RequestParam("source") String source,HttpSession session) throws IOException {
        try {
            autoService.copyDirectory(Paths.get(source),session);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return ResponseEntity.ok("文件拷贝成功：" + source);
    }

    // 新增删除文件夹的方法
    @PostMapping("/delete")
    public ResponseEntity<String> delete(@RequestParam("stu_id")String stu_id) {
        try {
//            autoService.deleteDirectory(stu_id);
            autoService.forcedeleteDirectory(stu_id);
        } catch (IOException e) {
            throw new RuntimeException("删除文件夹失败", e);
        }
        return ResponseEntity.ok("文件夹删除成功：");
    }


    @GetMapping("/shiyan")
    public String getFileContent() {
        // 文件路径
        String filePath = "C:\\project\\experiment.txt";

//        String filePath = "F:\\Keil C51-EL-EMCU-IV\\shiyan.txt";
        StringBuilder content = new StringBuilder();

        log.info("读取文件"+filePath);

        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        } catch (IOException e) {
            return "文件读取失败: " + e.getMessage();
        }
        return content.toString();
    }
}
