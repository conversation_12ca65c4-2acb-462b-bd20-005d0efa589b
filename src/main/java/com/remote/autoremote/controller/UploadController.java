package com.remote.autoremote.controller;

import com.remote.autoremote.service.UploadService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@RestController
@RequestMapping("/remote_server/upload")
@CrossOrigin(origins = "*")
public class UploadController {

    private static final Logger log = LoggerFactory.getLogger(UploadController.class);
    @Autowired
    private UploadService uploadService;

    @Value("${file.TARGET_DIRECTORY}")
    private String source;


    @PostMapping("/teacher")
    public ResponseEntity<String> upload(MultipartFile file) {
        try {
            uploadService.upload(file);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return ResponseEntity.ok("上传成功"+file.getOriginalFilename());
    }


    @PostMapping("/teachers")
    public ResponseEntity<String> uploadMultiFile(List<MultipartFile> files, @RequestParam("filePath")String filePath) {
        try {
            // 调用服务类的上传方法
            uploadService.uploads(files,filePath);

            // 生成文件名列表
            StringBuilder fileNames = new StringBuilder("文件上传成功：");
            for (MultipartFile file : files) {
                String originalFilename = file.getOriginalFilename();
                if (originalFilename != null && !originalFilename.isEmpty()) {
                    fileNames.append(originalFilename).append(", ");
                }
            }

            // 去掉最后一个多余的逗号和空格
            String responseMessage = fileNames.toString().replaceAll(", $", "");

            // 返回成功响应，附带文件名信息
            return ResponseEntity.ok(responseMessage);

        } catch (Exception e) {
            // 记录错误日志
            e.printStackTrace();

            // 返回错误响应，包含错误信息
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("文件上传失败：" + e.getMessage());
        }
    }


    @PostMapping("/getDirList")
    public ResponseEntity<String> getDirList(String path) {
        List<String> folderList = uploadService.getFolderList(source);
        return ResponseEntity.ok(folderList.toString());
    }

    /**
     * 下载文件到本地
     * @param path
     * @return
     */
    @GetMapping("/download-zip")
    public ResponseEntity<Void> downloadZip(@RequestParam("stu_id")String  stu_id,@RequestParam("program")String program,HttpServletResponse response) throws IOException {
//        String folderToCompress = "D:\\桌面\\123321"; // 要压缩的文件夹路径

        String folderToCompress =source + stu_id+"\\"+program;
        log.info(folderToCompress);
        // 设置响应的内容类型和文件名
        response.setContentType("application/zip");
        response.setHeader("Content-Disposition", "attachment; filename=\"compressedFolder.zip\"");

        // 输出流
        try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
            compressFolder(folderToCompress, zipOut);
        }

        return ResponseEntity.status(HttpStatus.OK).build();
    }

    // 将文件夹内容压缩为ZIP
    private void compressFolder(String sourceDirPath, ZipOutputStream zipOut) throws IOException {
        File sourceDir = new File(sourceDirPath);
        if (!sourceDir.isDirectory()) {
            throw new IllegalArgumentException("Source path must be a directory");
        }

        compressDirectoryToZip(sourceDir, sourceDir.getName(), zipOut);
    }

    private void compressDirectoryToZip(File folder, String parentDirName, ZipOutputStream zipOut) throws IOException {
        File[] files = folder.listFiles();
        if (files == null) return;

        for (File file : files) {
            if (file.isDirectory()) {
                compressDirectoryToZip(file, parentDirName + "/" + file.getName(), zipOut);
            } else {
                try (FileInputStream fis = new FileInputStream(file)) {
                    ZipEntry zipEntry = new ZipEntry(parentDirName + "/" + file.getName());
                    zipOut.putNextEntry(zipEntry);

                    byte[] buffer = new byte[1024];
                    int length;
                    while ((length = fis.read(buffer)) >= 0) {
                        zipOut.write(buffer, 0, length);
                    }

                    zipOut.closeEntry();
                }
            }
        }
    }
}
