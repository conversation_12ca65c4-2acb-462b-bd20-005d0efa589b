package com.remote.autoremote.controller;

import com.remote.autoremote.domain.SysUser;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpSession;

@RestController
public class UserController {

    @Value("${file.source}")
    private String source;

    @RequestMapping("/getUserId")
    public String getUserId(HttpSession session) {
        // 从 session 中获取 currentUser 对象
        SysUser currentUser = (SysUser) session.getAttribute("currentUser");
        if (currentUser != null) {
            // 获取并返回 userId
            Long userId = currentUser.getId();
            return "当前用户ID: " + userId;
        } else {
            return "未登录";
        }
    }

    @RequestMapping("/login")
    public String login(@RequestParam String userLoginName, @RequestParam String password, HttpSession session) {
        // 模拟用户验证过程
        if ("admin".equals(userLoginName) && "password123".equals(password)) {
            // 创建一个 SysUser 对象模拟当前登录的用户
            SysUser currentUser = new SysUser();
            currentUser.setId(123L); // 模拟的 userId
            currentUser.setUserLoginName(userLoginName);
            currentUser.setUserName("管理员");
            currentUser.setUserEmail("<EMAIL>");
            currentUser.setUserPhone("1234567890");
            currentUser.setForbidden("0"); // 用户未禁用

            // 将用户信息存储到 session 中
            session.setAttribute("currentUser", currentUser);
            System.out.println(currentUser);
            return "登录成功";
        } else {
            return "用户名或密码错误";
        }
    }

}
