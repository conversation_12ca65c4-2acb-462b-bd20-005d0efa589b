package com.remote.autoremote.controller;

import com.remote.autoremote.service.VideoService;
import com.remote.autoremote.websocket.MyWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

@RestController
@RequestMapping("/remote_server/video")
@CrossOrigin(value = "*")
public class VideoController {


    @Autowired
    private VideoService videoService;

    @Autowired
    private MyWebSocketHandler myWebSocketHandler;

    @Value("${file.FFMPEG_PATH}")
    private String ffmpegPath;

    @PostMapping("/record")
    public ResponseEntity<String> record_video(@RequestParam("stu_id")String  stu_id,@RequestParam("program") String program)
    {
        String s = videoService.record_video(stu_id,program);
        return ResponseEntity.ok(s);
    }

    @PostMapping("/test")
    public ResponseEntity<String> test()
    {
        String checkCommand = String.format("%s -f gdigrab -i desktop -t 5 -an -vn -loglevel quiet", ffmpegPath);
        ProcessBuilder processBuilder = new ProcessBuilder(checkCommand.split(" "));
        processBuilder.environment().put("PATH", System.getenv("PATH") + ";path_to_ffmpeg");
        try {
            Process process = processBuilder.start();
            int exitCode = process.waitFor();
            return ResponseEntity.ok("捕捉成功");
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
            return ResponseEntity.ok("捕捉失败");
        }
    }


    /**
     * 屏幕截图调用的方法
     * @param stu_id
     * @param program
     * @return
     */
    @PostMapping("/screen")
    public ResponseEntity<String> record_screen(@RequestParam("stu_id")String  stu_id,@RequestParam("program") String program)
    {
        return ResponseEntity.ok(videoService.screen(stu_id,program));
    }
}
