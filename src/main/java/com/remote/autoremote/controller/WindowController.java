package com.remote.autoremote.controller;

import com.remote.autoremote.utils.WindowTopper;
import com.remote.autoremote.utils.WindowTopperV2;
import com.remote.autoremote.utils.WindowTopperV3;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpSession;
import java.io.IOException;

/**
 * @Author: hh
 * @Date: 2025-04-24-09:24
 * @Description:
 */
@RestController
@CrossOrigin(origins = "*")
@RequestMapping("/remote_server/window")
public class WindowController {


    @PostMapping("/upo")
    public ResponseEntity<String> window(@RequestParam(defaultValue = "UPO") String keyword) {
//        WindowTopperV2.minimizeAllExcept(keyword);
        WindowTopperV2.setTopMostWindow(keyword);
//        WindowTopperV3.minimizeAllExcept(keyword);
        return ResponseEntity.ok("尝试置顶窗口，关键词：" + keyword);
    }


}
