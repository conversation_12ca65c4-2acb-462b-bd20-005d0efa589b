package com.remote.autoremote.domain;

public class SysUser {

    private Long id;                // 用户ID
    private String userLoginName;   // 用户登录名
    private String userPass;        // 用户密码
    private String userName;        // 用户姓名
    private String userEmail;       // 用户邮箱
    private String userPhone;       // 用户手机号
    private String forbidden;       // 用户是否禁用 (0: 未禁用, 1: 已禁用)

    // 无参构造方法
    public SysUser() {}

    // 带参构造方法
    public SysUser(Long id, String userLoginName, String userPass, String userName, String userEmail, String userPhone, String forbidden) {
        this.id = id;
        this.userLoginName = userLoginName;
        this.userPass = userPass;
        this.userName = userName;
        this.userEmail = userEmail;
        this.userPhone = userPhone;
        this.forbidden = forbidden;
    }

    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserLoginName() {
        return userLoginName;
    }

    public void setUserLoginName(String userLoginName) {
        this.userLoginName = userLoginName;
    }

    public String getUserPass() {
        return userPass;
    }

    public void setUserPass(String userPass) {
        this.userPass = userPass;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public String getForbidden() {
        return forbidden;
    }

    public void setForbidden(String forbidden) {
        this.forbidden = forbidden;
    }

    @Override
    public String toString() {
        return "SysUser{" +
                "id=" + id +
                ", userLoginName='" + userLoginName + '\'' +
                ", userPass='" + userPass + '\'' +
                ", userName='" + userName + '\'' +
                ", userEmail='" + userEmail + '\'' +
                ", userPhone='" + userPhone + '\'' +
                ", forbidden='" + forbidden + '\'' +
                '}';
    }
}
