package com.remote.autoremote.service;

import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.nio.file.Path;

public interface AutoService {

    void copyDirectory(Path path, HttpSession session) throws IOException;

    void deleteDirectory(String path) throws IOException;

    void forcedeleteDirectory(String id) throws IOException;

    String  checkDir(String dir);

    String CopyDirByLoginName(String loginName, String source);

    String createByRecordDir(String stuId, String dir);

    boolean checkDirIsTrue(String stuId, String dir);
}
