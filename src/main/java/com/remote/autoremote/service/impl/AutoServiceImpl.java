package com.remote.autoremote.service.impl;

import com.remote.autoremote.domain.SysUser;
import com.remote.autoremote.service.AutoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpSession;
import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;

@Service
public class AutoServiceImpl implements AutoService {

    private static final Logger logger = LoggerFactory.getLogger(AutoServiceImpl.class);
//    private static final String TARGET_DIRECTORY = "D:\\桌面\\"; // 使用双反斜杠
    @Value("${file.TARGET_DIRECTORY}")
    private String TARGET_DIRECTORY;

    @Value("${file.source}")
    private String targetDirectory;



    @Override
    public void copyDirectory(Path source, HttpSession session) throws IOException {
        // 创建目标文件夹
        SysUser currentUser = (SysUser) session.getAttribute("currentUser");
        String id = currentUser.getId().toString();
        Path target = Paths.get(TARGET_DIRECTORY +id );
        Files.createDirectories(target);

        // 遍历源文件夹的所有文件和子目录
        Files.walkFileTree(source, new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                // 计算目标文件的路径
                Path targetFile = target.resolve(source.relativize(file));
                // 复制文件到目标路径
                Files.copy(file, targetFile, StandardCopyOption.REPLACE_EXISTING);
                return FileVisitResult.CONTINUE;
            }

            @Override
            public FileVisitResult preVisitDirectory(Path dir, BasicFileAttributes attrs) throws IOException {
                // 创建子目录
                Path targetDir = target.resolve(source.relativize(dir));
                Files.createDirectories(targetDir);
                return FileVisitResult.CONTINUE;
            }
        });
    }

    @Override
    public void deleteDirectory(String id) throws IOException {
        Path target = Paths.get(TARGET_DIRECTORY +id );
        logger.info(target.toString());
        if (Files.exists(target)) {
//        先将文件备份到对应的文件夹下
//        Path source = Paths.get(TARGET_DIRECTORY +id );
        try {
            copyFolderContents(TARGET_DIRECTORY +id ,targetDirectory+id);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        logger.info("拷贝成功{}", id);
//      移除文件夹
        Files.walkFileTree(target, new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                Files.delete(file);
                return FileVisitResult.CONTINUE;
            }

            @Override
            public FileVisitResult postVisitDirectory(Path dir, IOException exc) throws IOException {
                Files.delete(dir);
                return FileVisitResult.CONTINUE;
            }
        });
        }
        else {
            throw new RuntimeException("您所查找的文件夹不存在");
        }
    }

    @Override
    public void forcedeleteDirectory(String id) throws IOException {
        Path target = Paths.get(TARGET_DIRECTORY +id );
        logger.info(target.toString());
        if (Files.exists(target)) {
//        先将文件备份到对应的文件夹下
//        Path source = Paths.get(TARGET_DIRECTORY +id );
            try {
                copyFolderContents(TARGET_DIRECTORY +id ,targetDirectory+id);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            logger.info("拷贝成功{}", id);
//      移除文件夹
            // 2. 强制删除（无论是否占用/只读）
            try {
                Files.walkFileTree(target, new SimpleFileVisitor<Path>() {
                    @Override
                    public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) {
                        try {
                            // 解除只读属性（适用于 Windows）
                            try {
                                Files.setAttribute(file, "dos:readonly", false);
                            } catch (Exception ignore) {}

                            Files.deleteIfExists(file);
                            logger.debug("已删除文件: {}", file);
                        } catch (Exception e) {
                            logger.warn("文件删除失败（已跳过）: {}", file, e);
                        }
                        return FileVisitResult.CONTINUE;
                    }

                    @Override
                    public FileVisitResult postVisitDirectory(Path dir, IOException exc) {
                        try {
                            Files.deleteIfExists(dir);
                            logger.debug("已删除目录: {}", dir);
                        } catch (Exception e) {
                            logger.warn("目录删除失败（已跳过）: {}", dir, e);
                        }
                        return FileVisitResult.CONTINUE;
                    }
                });

                logger.info("强制删除目录完成: {}", target);
            } catch (IOException e) {
                logger.error("walkFileTree 异常（删除未完全）", e);
            }

        }
        else {
            throw new RuntimeException("您所查找的文件夹不存在");
        }
    }


    @Override
    public String checkDir(String dir) {
        // 创建 File 对象，拼接目标路径
        File directory = new File(TARGET_DIRECTORY, dir);
        // 检查目录是否存在
        if (!directory.exists()) {
            // 目录不存在，尝试创建
            boolean created = directory.mkdirs(); // 使用 mkdirs() 支持多级目录创建
            if (!created) {
                throw new RuntimeException("目录创建失败：" + directory.getAbsolutePath());
            }
        } else if (!directory.isDirectory()) {
            // 如果存在但不是目录，抛出异常
            throw new RuntimeException("目标路径已存在但不是目录：" + directory.getAbsolutePath());
        }

        // 返回目录的绝对路径
        return directory.getAbsolutePath();
    }



    @Override
    public String CopyDirByLoginName(String loginName, String source) {
        String studentDir = createStudentDir(loginName,source);
        System.out.println(studentDir);
        copyFolderContents(source,studentDir);
        return "拷贝"+loginName+"源文件夹"+source;
    }

    @Override
    public String createByRecordDir(String stuId, String dir) {
//        获取源文件夹的路径
        String studentDir = targetDirectory + "//" + stuId + "//" + dir;
        copyFolderContents(studentDir,TARGET_DIRECTORY+stuId + "//" + dir);
        return studentDir;
    }

    @Override
    public boolean checkDirIsTrue(String stuId, String dir) {
        // 构造目标路径
        String studentDir = targetDirectory  + stuId + "//" + dir;
        logger.info(studentDir);
        // 检查路径是否存在且是否为目录
        File file = new File(studentDir);
        if (file.exists() && file.isDirectory()) {
            return true;
        }
        return false;
    }


    private String createStudentDir(String stuId, String source) {
        // 获取传入路径的最后一级目录名称
        String sourceFolderName = new File(source).getName();

        // 构建目标路径：TARGET_DIRECTORY/stuId/sourceFolderName
        File studentDir = new File(TARGET_DIRECTORY, stuId + File.separator + sourceFolderName);


        // 检查父目录是否存在，如果不存在，则创建父目录
        File parentDir = studentDir.getParentFile();
        if (!parentDir.exists()) {
            boolean parentCreated = parentDir.mkdirs(); // 使用 mkdirs() 递归创建父目录
            if (!parentCreated) {
                logger.error("无法创建父目录: {}", parentDir.getAbsolutePath());
                logger.error("是否有写权限: {}", parentDir.canWrite());
                throw new RuntimeException("创建父目录失败：" + parentDir.getAbsolutePath());
            }
            logger.info("父目录创建成功: {}", parentDir.getAbsolutePath());
        }
        // 检查目标目录是否存在，不存在则创建
        if (!studentDir.exists()) {
            boolean created = studentDir.mkdir();
            if (!created) {
                logger.error("无法创建目录: {}", studentDir.getAbsolutePath());
                logger.error("父目录是否存在: {}", parentDir.exists());
                logger.error("是否有写权限: {}", parentDir.canWrite());
                throw new RuntimeException("创建学生目录失败：" + studentDir.getAbsolutePath());
            }
            logger.info("创建成功的学生目录: {}", studentDir.getAbsolutePath());
        }

        return studentDir.getAbsolutePath();
    }



    public void copyFolderContents(String sourceDirectoryStr, String targetDirectoryStr) {
        Path sourceDirectory = Paths.get(sourceDirectoryStr);
        Path targetDirectory = Paths.get(targetDirectoryStr);

        // 校验源目录是否存在并且是目录
        if (!Files.exists(sourceDirectory) || !Files.isDirectory(sourceDirectory)) {
            throw new IllegalArgumentException("源路径不是有效的目录：" + sourceDirectoryStr);
        }

        try {
            // 确保目标目录存在
            if (!Files.exists(targetDirectory)) {
                Files.createDirectories(targetDirectory);
            }

            // 遍历源目录的所有文件和子目录
            Files.walkFileTree(sourceDirectory, new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                    // 复制文件到目标目录（去掉源文件夹前缀）
                    Path relativePath = sourceDirectory.relativize(file);
                    Path targetFile = targetDirectory.resolve(relativePath);
                    Files.copy(file, targetFile, StandardCopyOption.REPLACE_EXISTING);
//                    System.out.println("复制文件：" + file + " -> " + targetFile);
                    return FileVisitResult.CONTINUE;
                }

                @Override
                public FileVisitResult preVisitDirectory(Path dir, BasicFileAttributes attrs) throws IOException {
                    // 在目标目录中创建对应的子目录
                    Path relativePath = sourceDirectory.relativize(dir);
                    Path targetDir = targetDirectory.resolve(relativePath);
                    if (!Files.exists(targetDir)) {
                        Files.createDirectories(targetDir);
                    }
                    return FileVisitResult.CONTINUE;
                }
            });

        } catch (IOException e) {
            throw new RuntimeException("文件复制失败：" + e.getMessage(), e);
        }
    }



}
