package com.remote.autoremote.service.impl;

import com.remote.autoremote.service.UploadService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Service
public class UploadServiceImpl implements UploadService {

    @Value("${file.TARGET_DIRECTORY}")
    private String source;

    @PostConstruct
    public void init() {
        System.out.println("Configured upload directory: " + source);

        // 如果目标文件夹不存在，则创建它
        File directory = new File(source);
        if (!directory.exists()) {
            boolean created = directory.mkdirs();
            if (created) {
                System.out.println("Upload directory created: " + source);
            } else {
                System.out.println("Failed to create upload directory: " + source);
            }
        }
    }

    public void upload(MultipartFile file) {
        if (file.isEmpty()) {
            System.out.println("File is empty. Upload failed.");
            return;
        }

        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.isEmpty()) {
            System.out.println("Invalid file name. Upload failed.");
            return;
        }

        // 检查文件名的合法性
        if (!isValidFileName(originalFilename)) {
            System.out.println("Invalid file name detected. Upload failed.");
            return;
        }

        // 创建目标文件
        File targetFile = new File(source, originalFilename);

        // 如果目标文件已存在，考虑重命名或覆盖
        if (targetFile.exists()) {
            System.out.println("File already exists: " + targetFile.getAbsolutePath());
            targetFile = getUniqueFile(targetFile);
        }

        try {
            // 保存文件到目标路径
            file.transferTo(targetFile);
            System.out.println("File uploaded successfully to: " + targetFile.getAbsolutePath());
        } catch (IOException e) {
            e.printStackTrace();
            System.out.println("File upload failed: " + e.getMessage());
        }
    }

    @Override
    public void uploads(List<MultipartFile> files, String filePath) {
        // 创建目标文件夹
        filePath = filePath.replace(",", ""); // 移除路径中的逗号
        File targetDir = new File(filePath);
        if (!targetDir.exists()) {
            // 如果文件夹不存在，则创建它
            boolean dirCreated = targetDir.mkdirs(); // 创建多层目录
            if (dirCreated) {
                System.out.println("目标文件夹创建成功: " + targetDir.getAbsolutePath());
            } else {
                System.out.println("目标文件夹创建失败: " + targetDir.getAbsolutePath());
                return; // 如果文件夹创建失败，结束方法
            }
        }

        for (MultipartFile file : files) {
            if (file.isEmpty()) {
                System.out.println("Skipping empty file.");
                continue;
            }

            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.isEmpty()) {
                System.out.println("Skipping file with invalid name.");
                continue;
            }

            // 检查文件名合法性
            if (!isValidFileName(originalFilename)) {
                System.out.println("Skipping file with invalid characters: " + originalFilename);
                continue;
            }

            // 创建目标文件
            File targetFile = new File(targetDir, originalFilename); // 使用 targetDir 作为父目录

            // 如果目标文件已存在，生成唯一文件名
            if (targetFile.exists()) {
                targetFile = getUniqueFile(targetFile);
            }

            try {
                // 保存文件到目标路径
                file.transferTo(targetFile);
                System.out.println("File uploaded successfully: " + targetFile.getAbsolutePath());
            } catch (IOException e) {
                e.printStackTrace();
                System.out.println("Failed to upload file: " + originalFilename);
            }
        }
    }

    @Override
    public List<String> getFolderList(String path) {
        List<String> fileList = new ArrayList<>();
        File folder = new File(path);

        // 检查路径是否存在且是一个目录
        if (folder.exists() && folder.isDirectory()) {
            // 获取目录下的所有文件和文件夹
            listFilesAndFolders(folder, fileList);
        } else {
            throw new IllegalArgumentException("指定的路径不存在或不是一个目录");
        }

        return fileList;
    }

    private void listFilesAndFolders(File folder, List<String> fileList) {
        // 获取当前目录下的所有文件和文件夹
        File[] files = folder.listFiles();
        if (files != null) {
            for (File file : files) {
                // 添加文件或文件夹的名称
                fileList.add(file.getName()); // 或者使用 file.getAbsolutePath() 获取完整路径
                // 如果是目录，则递归调用
                if (file.isDirectory()) {
                    listFilesAndFolders(file, fileList);
                }
            }
        }
    }


    // 检查文件名是否合法
    private boolean isValidFileName(String filename) {
        return !filename.contains("/") && !filename.contains("\\") && !filename.contains("..");
    }

    // 获取唯一文件名（避免文件名冲突）
    private File getUniqueFile(File file) {
        String fileName = file.getName();
        String baseName = fileName.substring(0, fileName.lastIndexOf('.'));
        String extension = fileName.substring(fileName.lastIndexOf('.'));
        int counter = 1;
        while (file.exists()) {
            file = new File(file.getParent(), baseName + "_" + counter++ + extension);
        }
        return file;
    }
}
