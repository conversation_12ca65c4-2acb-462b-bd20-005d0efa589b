package com.remote.autoremote.service.impl;


import com.remote.autoremote.enums.FileExtension;
import com.remote.autoremote.service.AutoService;
import com.remote.autoremote.service.VideoService;
import com.remote.autoremote.utils.ScreenRecorder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;


@Service
public class VideoServiceImpl implements VideoService {

    private static final Logger log = LoggerFactory.getLogger(VideoServiceImpl.class);


    @Autowired
    private ScreenRecorder screenRecorder;

    @Autowired
    private AutoService autoService;


    @Override
    public String record_video(String stu_id, String program) {
//        启动录屏功能
        String stu_dir = autoService.checkDir(stu_id+"//"+program+"//"+FileExtension.RESULT.getExtension());
        log.info(stu_dir);
        String file_path = Paths.get(stu_dir, stu_id +FileExtension.MP4.getExtension()).toString();
        screenRecorder.startRecording(file_path);
        try {
            File videoFile = new File(file_path);
            byte[] fileContent = Files.readAllBytes(videoFile.toPath());
            // 转换为 Base64 编码
            return "data:video/mp4;base64," + Base64.getEncoder().encodeToString(fileContent);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 截图屏幕
     * @param stu_id
     * @param program  实验名称
     * @return
     */
    @Override
    public String screen(String stu_id, String program) {
        String dir = autoService.checkDir(stu_id+"//"+program+"//"+FileExtension.RESULT.getExtension());
        log.info(dir);
        String formattedDate = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss").format(new Date());

        String file_path = Paths.get(dir, formattedDate + FileExtension.PNG.getExtension()).toString();
        try {
            screenRecorder.recordScreen(file_path);
            // 读取截图文件并转换为 Base64 编码
            byte[] fileContent = Files.readAllBytes(Paths.get(file_path));
            String base64Image = Base64.getEncoder().encodeToString(fileContent);

            // 返回 Base64 编码的图片
            return "data:image/png;base64," + base64Image;
        } catch (Exception e) {
            throw new RuntimeException("截图失败或文件处理失败",e);
        }
    }

}
