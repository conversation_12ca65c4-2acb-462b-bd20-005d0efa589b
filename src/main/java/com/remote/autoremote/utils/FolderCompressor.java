package com.remote.autoremote.utils;

import java.io.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class FolderCompressor {

    public static void compressFolder(String sourceDirPath, OutputStream outputStream) throws IOException {
        File sourceDir = new File(sourceDirPath);
        if (!sourceDir.isDirectory()) {
            throw new IllegalArgumentException("Source path must be a directory");
        }

        try (ZipOutputStream zipOut = new ZipOutputStream(outputStream)) {
            compressDirectoryToZip(sourceDir, sourceDir.getName(), zipOut);
        }
    }

    private static void compressDirectoryToZip(File folder, String parentDirName, ZipOutputStream zipOut) throws IOException {
        // Get all files and subdirectories inside the folder
        File[] files = folder.listFiles();
        if (files == null) return;

        for (File file : files) {
            if (file.isDirectory()) {
                // If it's a directory, recursively zip its contents
                compressDirectoryToZip(file, parentDirName + "/" + file.getName(), zipOut);
            } else {
                // If it's a file, zip it
                try (FileInputStream fis = new FileInputStream(file)) {
                    // Create a zip entry with the file's relative path
                    ZipEntry zipEntry = new ZipEntry(parentDirName + "/" + file.getName());
                    zipOut.putNextEntry(zipEntry);

                    // Write the file content to the zip output stream
                    byte[] buffer = new byte[1024];
                    int length;
                    while ((length = fis.read(buffer)) >= 0) {
                        zipOut.write(buffer, 0, length);
                    }

                    zipOut.closeEntry();
                }
            }
        }
    }

    public static void main(String[] args) throws IOException {
        // For example, compress the folder "myFolder" and output to a file
        String folderToCompress = "D:\\桌面\\123321";
        try (FileOutputStream fileOut = new FileOutputStream("compressedFolder.zip")) {
            compressFolder(folderToCompress, fileOut);
        }
    }
}
