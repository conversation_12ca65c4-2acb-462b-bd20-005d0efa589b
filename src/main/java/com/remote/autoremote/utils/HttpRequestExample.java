package com.remote.autoremote.utils;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

public class HttpRequestExample {
    public static void main(String[] args) {

        // 创建 RestTemplate 实例
        RestTemplate restTemplate = new RestTemplate();

        // 设置请求 URL
        String url = "http://localhost:8088/video/record";

        // 构造请求头
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/x-www-form-urlencoded");
//         如果是json格式使用这个
//        headers.setContentType(MediaType.APPLICATION_JSON);

        // 构造请求体
        MultiValueMap requestBody = new LinkedMultiValueMap();
        requestBody.add("stu_id", "123456");


        // 将请求头和体封装为 HttpEntity
        HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);

        // 发送 POST 请求
        ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                String.class
        );

        // 打印响应
        System.out.println("Response: " + response.getBody());
    }
}
