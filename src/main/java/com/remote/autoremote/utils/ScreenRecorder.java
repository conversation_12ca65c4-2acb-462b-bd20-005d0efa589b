package com.remote.autoremote.utils;

import com.remote.autoremote.websocket.MyWebSocketHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import java.awt.*;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

@Component
public class ScreenRecorder {


    private static final Logger log = LoggerFactory.getLogger(ScreenRecorder.class);

    @Value("${file.FFMPEG_PATH}")
    private String FFMPEG_PATH;
    //    private static final String OUTPUT_PATH = "D:\\Users\\hh\\Desktop\\output.mp4"; // 输出路径
    private static final int FRAME_RATE = 30; // 帧率
    private static final int RECORDING_TIME = 10; // 录制时间（秒）




    @Autowired
    private MyWebSocketHandler myWebSocketHandler;


//    @Async
    public void startRecording(String out_path) {
        try {
//            recordScreen(out_path);
            recordScreen_power(out_path);
            // 录屏成功后，发送消息通知客户端
//            myWebSocketHandler.broadcastMessage("文件录制成功" + out_path);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    public void recordScreen(String output) throws IOException, InterruptedException {
        // 获取屏幕尺寸
        // 检查系统是否为无头环境
        if (GraphicsEnvironment.isHeadless()) {
            System.err.println("当前环境为无头模式，无法进行屏幕录制操作。");
            return; // 或者抛出异常
        } else {
            // 获取屏幕尺寸
            Dimension screenSize = Toolkit.getDefaultToolkit().getScreenSize();
            String screenResolution = (int) screenSize.getWidth() + "x" + (int) screenSize.getHeight();
            log.info(screenResolution);
            // Print parameters to be used
//            System.out.println("Starting screen recording with the following parameters:");
//            System.out.println("FFmpeg Path: " + FFMPEG_PATH);
//            System.out.println("Output Path: " + OUTPUT_PATH);
//            System.out.println("Frame Rate: " + FRAME_RATE);
//            System.out.println("Recording Time (seconds): " + RECORDING_TIME);
//            System.out.println("Screen Resolution: " + screenResolution);

//        // 添加 -t 参数以设置录制时长
//        String command = String.format("%s -video_size %s -framerate %d -f gdigrab -i desktop -t %d -c:v libx264 -pix_fmt yuv420p %s",
//                FFMPEG_PATH, screenResolution, FRAME_RATE, RECORDING_TIME, OUTPUT_PATH);
//

            // 添加 -y 参数以自动覆盖已存在的文件
//            String command = String.format("%s -y -video_size %s -framerate %d -f gdigrab -i desktop -t %d -c:v libx264 -pix_fmt yuv420p -loglevel info %s",
//                    FFMPEG_PATH, screenResolution, FRAME_RATE, RECORDING_TIME, output);
//            log.info("***************************" + command);
            String command = String.format("%s -y -f gdigrab -video_size %s -i desktop -vframes 1 %s",
                    FFMPEG_PATH, screenResolution, output);

//            延迟0.1秒钟截屏
            Thread.sleep(100);

            Process process = Runtime.getRuntime().exec(command);
            log.info("***************************" + process.toString());
            // 输出流处理
            new Thread(() -> {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        System.out.println("FFmpeg Output: " + line);
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }).start();

            // Error stream handling
            new Thread(() -> {
                try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
                    String line;
                    while ((line = errorReader.readLine()) != null) {
                        System.err.println("FFmpeg Error: " + line);
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }).start();

            // 等待进程结束
            try {
                int exitCode = process.waitFor();
                System.out.println("Process exited with code: " + exitCode);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    public void recordScreen_power(String output) throws IOException {
        // 获取屏幕尺寸
        if (GraphicsEnvironment.isHeadless()) {
            System.err.println("当前环境为无头模式，无法进行屏幕录制操作。");
            return; // 或者抛出异常
        }

        // 获取屏幕尺寸
        Dimension screenSize = Toolkit.getDefaultToolkit().getScreenSize();
        String screenResolution = (int) screenSize.getWidth() + "x" + (int) screenSize.getHeight();

        // 使用 StringBuilder 构建命令字符串
        StringBuilder commandBuilder = new StringBuilder();
//        commandBuilder.append("\"").append(FFMPEG_PATH).append("\" ")
//                .append("-y ")
//                .append("-f gdigrab ")
//                .append("-video_size ").append(screenResolution).append(" ")
//                .append("-framerate ").append(FRAME_RATE).append(" ")
//                .append("-i desktop ")
//                .append("-t ").append(RECORDING_TIME).append(" ")
//                .append("-c:v libx264 ")
//                .append("-pix_fmt yuv420p ")
//                .append("-loglevel info ")
////                .append("-analyzeduration 10M ")
////                .append("-probesize 10M ")
//                .append("\"").append(output).append("\"");

//        commandBuilder.append("\"").append(FFMPEG_PATH).append("\" ")
//                .append("-y ") // 覆盖输出文件的全局选项，必须放在最前面
//                .append("-f gdigrab ") // 输入格式设置（gdigrab 用于桌面抓取）
//                .append("-framerate ").append(FRAME_RATE).append(" ") // 设置帧率
//                .append("-video_size ").append(screenResolution).append(" ") // 设置视频分辨率
//                .append("-i desktop ") // 指定输入源（桌面）
//                .append("-t ").append(RECORDING_TIME).append(" ") // 设置录制时长
//                .append("-c:v libx264 ") // 指定视频编解码器为 libx264
//                .append("-pix_fmt yuv420p ") // 设置像素格式
//                .append("-loglevel info ") // 设置日志级别
////                .append("-analyzeduration 10M ") // 可选：指定分析时长
////                .append("-probesize 10M ") // 可选：指定探测大小
//                .append("\"").append(output).append("\""); // 输出文件路径

        commandBuilder.append("\"").append(FFMPEG_PATH).append("\" ")
                .append("-y ") // 覆盖输出文件的全局选项，必须放在最前面
                .append("-f gdigrab ") // 输入格式设置（gdigrab 用于桌面抓取）
                .append("-framerate ").append(FRAME_RATE).append(" ") // 设置帧率
                .append("-video_size ").append(screenResolution).append(" ") // 设置视频分辨率
                .append("-i desktop ") // 指定输入源（桌面）
                .append("-t ").append(RECORDING_TIME).append(" ") // 设置录制时长
                .append("-vf \"scale=trunc(iw/2)*2:trunc(ih/2)*2\" ") // 使用 scale 滤镜调整分辨率为 2 的倍数
                .append("-c:v libx264 ") // 指定视频编解码器为 libx264
                .append("-pix_fmt yuv420p ") // 设置像素格式
                .append("-loglevel info ") // 设置日志级别
//                .append("-analyzeduration 10M ") // 可选：指定分析时长
//                .append("-probesize 10M ") // 可选：指定探测大小
                .append("\"").append(output).append("\""); // 输出文件路径


        String command = commandBuilder.toString();
        log.info("***************************" + command);

        ProcessBuilder processBuilder = new ProcessBuilder(command.split(" "));
        processBuilder.environment().put("PATH", System.getenv("PATH") + ";" + FFMPEG_PATH);

        processBuilder.redirectErrorStream(true); // 合并标准输出和错误输出
        Process process = null;

        try {
            Thread.sleep(100);  // 等待半秒钟
            process = processBuilder.start();
            log.info("命令执行成功，接下来的代码执行中");

            // 输出流处理
            Process finalProcess = process;
            new Thread(() -> {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(finalProcess.getInputStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        System.out.println("FFmpeg Output: " + line);
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }).start();

            // 错误流处理
            new Thread(() -> {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(finalProcess.getErrorStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        System.err.println("FFmpeg Error: " + line);
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }).start();

            // 等待进程结束并检查退出码
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                System.err.println("FFmpeg 进程异常退出，退出码：" + exitCode);
            } else {
                System.out.println("屏幕录制完成，输出文件路径：" + output);
            }
        } catch (IOException | InterruptedException e) {
            System.err.println("执行 FFmpeg 命令时发生错误。");
            e.printStackTrace();
        } finally {
            // 确保进程被销毁
            if (process != null && process.isAlive()) {
                process.destroyForcibly();
            }
        }
    }

}
