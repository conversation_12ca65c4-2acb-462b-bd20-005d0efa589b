package com.remote.autoremote.utils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

public class ScreenRecorder_1 {
    private static final String FFMPEG_PATH = "D:\\Users\\hh\\FFmpeg\\ffmpeg-7.1-essentials_build\\bin\\ffmpeg.exe"; // FFmpeg 路径
    private static final String OUTPUT_PATH = "D:\\Users\\hh\\Desktop\\output.mp4"; // 输出路径
    private static final String SCREEN_SIZE = "2560x1600"; // 屏幕分辨率
    private static final int FRAME_RATE = 30; // 帧率
    private static final int RECORDING_TIME = 10; // 录制时间（秒）

    public static void main(String[] args) {
        try {
            recordScreen();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static void recordScreen() throws IOException {
        // 添加 -t 参数以设置录制时长
        String command = String.format("%s -video_size %s -framerate %d -f gdigrab -i desktop -t %d -c:v libx264 -pix_fmt yuv420p %s",
                FFMPEG_PATH, SCREEN_SIZE, FRAME_RATE, RECORDING_TIME, OUTPUT_PATH);

        Process process = Runtime.getRuntime().exec(command);

        // 输出流处理
        new Thread(() -> {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    System.out.println(line);
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }).start();

        // 等待进程结束
        try {
            int exitCode = process.waitFor();
            System.out.println("Process exited with code: " + exitCode);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
}
