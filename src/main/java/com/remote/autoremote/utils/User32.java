package com.remote.autoremote.utils;

import com.sun.jna.*;
import com.sun.jna.platform.win32.WinDef.*;
import com.sun.jna.win32.StdCallLibrary;
import com.sun.jna.ptr.IntByReference;

public interface User32 extends StdCallLibrary {
    User32 INSTANCE = Native.load("user32", User32.class);

    interface WNDENUMPROC extends StdCallCallback {
        boolean callback(HWND hWnd, Pointer data);
    }

    boolean EnumWindows(WNDENUMPROC lpEnumFunc, Pointer data);
    int GetWindowTextA(HWND hWnd, byte[] lpString, int nMaxCount);
    boolean SetWindowPos(HWND hWnd, HWND hWndInsertAfter, int X, int Y, int cx, int cy, int uFlags);
    boolean SetForegroundWindow(HWND hWnd);
    boolean SetActiveWindow(HWND hWnd);
    boolean ShowWindow(HWND hWnd, int nCmdShow);
    boolean AttachThreadInput(int idAttach, int idAttachTo, boolean fAttach);
    HWND GetForegroundWindow();
    int GetWindowThreadProcessId(HWND hWnd, IntByReference pid);
    boolean IsIconic(HWND hWnd);
    boolean IsWindowVisible(HWND hWnd);
    int GetClassName(HWND hWnd, char[] lpClassName, int nMaxCount);
    boolean GetWindowRect(HWND hWnd, RECT rect);
}
