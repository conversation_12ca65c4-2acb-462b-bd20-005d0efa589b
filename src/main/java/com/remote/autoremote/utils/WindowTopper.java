package com.remote.autoremote.utils;

import com.sun.jna.*;
import com.sun.jna.platform.win32.WinDef.HWND;
import com.sun.jna.win32.StdCallLibrary;

public class WindowTopper {

    public interface User32 extends StdCallLibrary {
        User32 INSTANCE = Native.load("user32", User32.class);

        interface WNDENUMPROC extends StdCallCallback {
            boolean callback(HWND hWnd, Pointer data);
        }

        boolean EnumWindows(WNDENUMPROC lpEnumFunc, Pointer arg);
        int GetWindowTextA(HWND hWnd, byte[] lpString, int nMaxCount);
        boolean SetWindowPos(HWND hWnd, HWND hWndInsertAfter, int X, int Y, int cx, int cy, int uFlags);
        boolean SetForegroundWindow(HWND hWnd);
        boolean SetActiveWindow(HWND hWnd); // ✅ 新增接口
        boolean ShowWindow(HWND hWnd, int nCmdShow);
    }

    private static final HWND HWND_TOPMOST = new HWND(Pointer.createConstant(-1));
    private static final int SWP_NOMOVE = 0x0002;
    private static final int SWP_NOSIZE = 0x0001;
    private static final int SWP_SHOWWINDOW = 0x0040;

    public static void setTopMostWindow(String titlePrefix) {
        System.out.println("查找窗口标题以 \"" + titlePrefix + "\" 开头的窗口...");

        User32.INSTANCE.EnumWindows((hWnd, data) -> {
            byte[] windowText = new byte[512];
            User32.INSTANCE.GetWindowTextA(hWnd, windowText, 512);
            String title = Native.toString(windowText);

            if (title != null && title.startsWith(titlePrefix)) {
                System.out.println("找到窗口: " + title);

                // 强化处理：确保窗口激活 & 可见 & 可置顶
                User32.INSTANCE.ShowWindow(hWnd, 1);               // 恢复显示
                User32.INSTANCE.SetForegroundWindow(hWnd);         // 激活窗口
                User32.INSTANCE.SetActiveWindow(hWnd);             // 设置为活动窗口

                boolean result = User32.INSTANCE.SetWindowPos(
                        hWnd, HWND_TOPMOST, 0, 0, 0, 0,
                        SWP_NOMOVE | SWP_NOSIZE | SWP_SHOWWINDOW
                );

                System.out.println("窗口置顶结果: " + result);
                return false;
            }

            return true;
        }, null);
    }
}
