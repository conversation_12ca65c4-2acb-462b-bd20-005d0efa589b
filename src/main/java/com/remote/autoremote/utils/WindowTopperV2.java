package com.remote.autoremote.utils;

import com.sun.jna.*;
import com.sun.jna.platform.win32.WinDef.*;
import com.sun.jna.ptr.IntByReference;
import com.sun.jna.win32.StdCallLibrary;
import com.sun.jna.win32.W32APIOptions;

public class WindowTopperV2 {

    public interface User32 extends StdCallLibrary {
        User32 INSTANCE = Native.loadLibrary("user32", User32.class, W32APIOptions.DEFAULT_OPTIONS);

        interface WNDENUMPROC extends StdCallCallback {
            boolean callback(HWND hWnd, Pointer data);
        }

        boolean EnumWindows(WNDENUMPROC lpEnumFunc, Pointer data);
        int GetWindowTextA(HWND hWnd, byte[] lpString, int nMaxCount);
//        int GetClassName(HWND hWnd, char[] lpClassName, int nMaxCount);
        int GetClassName(HWND hWnd, char[] lpClassName, int nMaxCount);
        boolean GetWindowRect(HWND hWnd, RECT rect);
        boolean IsWindowVisible(HWND hWnd);
        boolean SetWindowPos(HWND hWnd, HWND hWndInsertAfter, int X, int Y, int cx, int cy, int uFlags);
        boolean SetForegroundWindow(HWND hWnd);
        boolean SetActiveWindow(HWND hWnd);
        boolean ShowWindow(HWND hWnd, int nCmdShow);
        boolean AttachThreadInput(int idAttach, int idAttachTo, boolean fAttach);
        HWND GetForegroundWindow();
        int GetWindowThreadProcessId(HWND hWnd, IntByReference pid);
        boolean IsIconic(HWND hWnd);
    }

    public interface Kernel32 extends StdCallLibrary {
        Kernel32 INSTANCE = Native.load("kernel32", Kernel32.class);
        int GetCurrentThreadId();
    }

    private static final HWND HWND_TOPMOST = new HWND(Pointer.createConstant(-1));
    private static final int SWP_NOMOVE = 0x0002;
    private static final int SWP_NOSIZE = 0x0001;
    private static final int SWP_SHOWWINDOW = 0x0040;

    // ✅ 置顶指定窗口
    public static void setTopMostWindow(String titlePrefix) {
        System.out.println("查找窗口标题以 \"" + titlePrefix + "\" 开头的窗口...");

        User32.INSTANCE.EnumWindows((hWnd, data) -> {
            byte[] windowText = new byte[512];
            User32.INSTANCE.GetWindowTextA(hWnd, windowText, 512);
            String title = Native.toString(windowText);

            if (title != null && title.startsWith(titlePrefix)) {
                System.out.println("找到窗口: " + title + " | HWND: " + Pointer.nativeValue(hWnd.getPointer()));

                HWND foreground = User32.INSTANCE.GetForegroundWindow();
                int currentThreadId = Kernel32.INSTANCE.GetCurrentThreadId();
                IntByReference fgPid = new IntByReference();
                int fgThreadId = User32.INSTANCE.GetWindowThreadProcessId(foreground, fgPid);

                User32.INSTANCE.AttachThreadInput(currentThreadId, fgThreadId, true);

                if (User32.INSTANCE.IsIconic(hWnd)) {
                    User32.INSTANCE.ShowWindow(hWnd, 9); // SW_RESTORE
                } else {
                    User32.INSTANCE.ShowWindow(hWnd, 6); // SW_MINIMIZE
                    try { Thread.sleep(50); } catch (InterruptedException ignored) {}
                    User32.INSTANCE.ShowWindow(hWnd, 9); // SW_RESTORE
                }

                User32.INSTANCE.SetForegroundWindow(hWnd);
                User32.INSTANCE.SetActiveWindow(hWnd);

                boolean result = User32.INSTANCE.SetWindowPos(
                        hWnd, HWND_TOPMOST, 0, 0, 0, 0,
                        SWP_NOMOVE | SWP_NOSIZE | SWP_SHOWWINDOW
                );

                System.out.println("窗口置顶结果: " + result);
                User32.INSTANCE.AttachThreadInput(currentThreadId, fgThreadId, false);
                return false; // 停止枚举
            }

            return true; // 继续枚举
        }, null);
    }

    // ✅ 最小化所有非指定标题的打开主窗口
    public static void minimizeAllExcept(String titlePrefix) {
        System.out.println("仅最小化非指定标题的“打开主窗口”...");

        User32.INSTANCE.EnumWindows((hWnd, data) -> {
            byte[] windowText = new byte[512];
            User32.INSTANCE.GetWindowTextA(hWnd, windowText, 512);
            String title = Native.toString(windowText);

            if (title == null || title.isEmpty()) return true;
            if (!User32.INSTANCE.IsWindowVisible(hWnd)) return true;

            // 获取窗口类名
            char[] className = new char[512];
            User32.INSTANCE.GetClassName(hWnd, className, 512);
            String clsName = Native.toString(className);

            // 跳过系统窗口
            if (clsName.contains("Tray") || clsName.contains("Notify") ||
                    clsName.contains("Shell") || clsName.contains("WorkerW") ||
                    clsName.contains("Progman")) {
                return true;
            }

            // 判断窗口尺寸
            RECT rect = new RECT();
            if (User32.INSTANCE.GetWindowRect(hWnd, rect)) {
                int width = rect.right - rect.left;
                int height = rect.bottom - rect.top;
                if (width < 100 || height < 100) return true;
            }

            // 处理窗口：最小化 or 保留
            if (!title.startsWith(titlePrefix)) {
                User32.INSTANCE.ShowWindow(hWnd, 6); // SW_MINIMIZE
            } else {
                User32.INSTANCE.ShowWindow(hWnd, 9); // SW_RESTORE
                User32.INSTANCE.SetForegroundWindow(hWnd);
                User32.INSTANCE.SetActiveWindow(hWnd);
                User32.INSTANCE.SetWindowPos(
                        hWnd, HWND_TOPMOST, 0, 0, 0, 0,
                        SWP_NOMOVE | SWP_NOSIZE | SWP_SHOWWINDOW
                );
                System.out.println("保留窗口: " + title);
            }

            return true;
        }, null);
    }
}
