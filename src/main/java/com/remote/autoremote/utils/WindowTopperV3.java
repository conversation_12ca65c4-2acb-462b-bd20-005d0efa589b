package com.remote.autoremote.utils;


import com.sun.jna.Native;
import com.sun.jna.Pointer;
import com.sun.jna.platform.win32.WinDef.*;
import com.sun.jna.ptr.IntByReference;
import com.sun.jna.platform.win32.WinDef.RECT;

public class WindowTopperV3 {

    private static final HWND HWND_TOPMOST = new HWND(Pointer.createConstant(-1));
    private static final int SWP_NOMOVE = 0x0002;
    private static final int SWP_NOSIZE = 0x0001;
    private static final int SWP_SHOWWINDOW = 0x0040;

    public static void setTopMostWindow(String titlePrefix) {
        System.out.println("查找窗口标题以 \"" + titlePrefix + "\" 开头的窗口...");

        User32.INSTANCE.EnumWindows((hWnd, data) -> {
            byte[] windowText = new byte[512];
            User32.INSTANCE.GetWindowTextA(hWnd, windowText, 512);
            String title = Native.toString(windowText);

            if (title != null && title.startsWith(titlePrefix)) {
                System.out.println("找到窗口: " + title);

                HWND foreground = User32.INSTANCE.GetForegroundWindow();
                int currentThreadId = Kernel32.INSTANCE.GetCurrentThreadId();
                IntByReference fgPid = new IntByReference();
                int fgThreadId = User32.INSTANCE.GetWindowThreadProcessId(foreground, fgPid);

                User32.INSTANCE.AttachThreadInput(currentThreadId, fgThreadId, true);

                if (User32.INSTANCE.IsIconic(hWnd)) {
                    User32.INSTANCE.ShowWindow(hWnd, 9); // SW_RESTORE
                } else {
                    User32.INSTANCE.ShowWindow(hWnd, 6); // SW_MINIMIZE
                    try { Thread.sleep(50); } catch (InterruptedException ignored) {}
                    User32.INSTANCE.ShowWindow(hWnd, 9); // SW_RESTORE
                }

                User32.INSTANCE.SetForegroundWindow(hWnd);
                User32.INSTANCE.SetActiveWindow(hWnd);

                boolean result = User32.INSTANCE.SetWindowPos(
                        hWnd, HWND_TOPMOST, 0, 0, 0, 0,
                        SWP_NOMOVE | SWP_NOSIZE | SWP_SHOWWINDOW
                );

                System.out.println("窗口置顶结果: " + result);
                User32.INSTANCE.AttachThreadInput(currentThreadId, fgThreadId, false);
                return false;
            }

            return true;
        }, null);
    }

    public static void minimizeAllExcept(String titlePrefix) {
        System.out.println("最小化除 \"" + titlePrefix + "\" 外的所有窗口...");

        User32.INSTANCE.EnumWindows((hWnd, data) -> {
            byte[] windowText = new byte[512];
            User32.INSTANCE.GetWindowTextA(hWnd, windowText, 512);
            String title = Native.toString(windowText);

            if (title == null || title.isEmpty()) return true;
            if (!User32.INSTANCE.IsWindowVisible(hWnd)) return true;

            char[] className = new char[512];
            User32.INSTANCE.GetClassName(hWnd, className, 512);
            String clsName = Native.toString(className);
            if (clsName.contains("Tray") || clsName.contains("Notify") || clsName.contains("Button") ||
                clsName.contains("Shell") || clsName.contains("Progman") || clsName.contains("WorkerW")) {
                return true;
            }

            RECT rect = new RECT();
            User32.INSTANCE.GetWindowRect(hWnd, rect);
            int width = rect.right - rect.left;
            int height = rect.bottom - rect.top;
            if (width < 100 || height < 100) return true;

            if (!title.startsWith(titlePrefix)) {
                User32.INSTANCE.ShowWindow(hWnd, 6); // SW_MINIMIZE
            } else {
                User32.INSTANCE.ShowWindow(hWnd, 9); // SW_RESTORE
                User32.INSTANCE.SetForegroundWindow(hWnd);
                User32.INSTANCE.SetActiveWindow(hWnd);
                User32.INSTANCE.SetWindowPos(
                        hWnd, HWND_TOPMOST, 0, 0, 0, 0,
                        SWP_NOMOVE | SWP_NOSIZE | SWP_SHOWWINDOW
                );
                System.out.println("保留窗口: " + title);
            }

            return true;
        }, null);
    }
}
