package com.remote.autoremote.websocket;

import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

@Component
public class MyWebSocketHandler extends TextWebSocketHandler {

    // 用一个Set来存储所有WebSocket会话
    private final Set<WebSocketSession> sessions = Collections.synchronizedSet(new HashSet<>());




    // 连接建立时，加入到会话列表
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        // 当客户端建立连接时，这个方法会被调用
        sessions.add(session);  // 将会话加入到集合中
        System.out.println("New connection established: " + session.getId());
    }

    // 连接关闭时，移除该会话
    @Override
    public void afterConnectionClosed(WebSocketSession session, org.springframework.web.socket.CloseStatus status) throws Exception {
        sessions.remove(session);  // 从集合中移除会话
        System.out.println("Connection closed: " + session.getId());
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        System.out.println("Received message from client: " + message.getPayload());
        // 你可以在这里选择处理接收到的消息或实现群发功能
        broadcastMessage(message.getPayload());
    }

    // 群发消息给所有连接的客户端
    public void broadcastMessage(String message) {
        for (WebSocketSession session : sessions) {
            if (session.isOpen()) {
                try {
                    session.sendMessage(new TextMessage(message));  // 向每个会话发送消息
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
