server:
  port: 8088
#  servlet:
#    context-path: /weiji


#file:
#  source: "D:\\Students\\"
#  FFMPEG_PATH: "D:\\Users\\hh\\FFmpeg\\ffmpeg-7.1-essentials_build\\bin\\ffmpeg.exe"
#  OUTPUT_PATH: "D:\\Users\\hh\\Desktop\\"
#  TARGET_DIRECTORY: "D:\\文档\\OneDrive\\Desktop\\"

file:
  source: "D:\\Students\\"
  FFMPEG_PATH: "C:\\project\\ffmpeg\\bin\\ffmpeg.exe"
#  FFMPEG_PATH: "E:\\java\\ffmpeg-20170130-cba4f0e-win64-static\\bin\\ffmpeg.exe"
  OUTPUT_PATH: "D:\\Users\\hh\\Desktop\\"
  TARGET_DIRECTORY: "C:\\Users\\<USER>\\Desktop\\"



spring:
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
