<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Demo</title>
</head>
<body>

<h2>WebSocket 测试页面</h2>

<button onclick="sendMessage()">发送消息</button>
<div id="messages"></div>

<script>
    // 建立WebSocket连接，连接到后端端口8088
    let socket = new WebSocket("ws://************:8088/ws");

    // 连接成功的回调
    socket.onopen = function() {
        console.log("WebSocket连接成功");
        document.getElementById("messages").innerHTML += "<p>连接已建立</p>";
    };

    // 接收消息的回调
    socket.onmessage = function(event) {
        console.log("收到消息：" + event.data);
        document.getElementById("messages").innerHTML += "<p>收到消息：" + event.data + "</p>";
    };

    // 连接关闭的回调
    socket.onclose = function() {
        console.log("WebSocket连接已关闭");
        document.getElementById("messages").innerHTML += "<p>连接已关闭</p>";
    };

    // 错误处理
    socket.onerror = function(error) {
        console.log("WebSocket发生错误：" + error);
        document.getElementById("messages").innerHTML += "<p>连接出错</p>";
    };

    // 点击按钮发送消息
    function sendMessage() {
        const message = "Hello Server!";
        console.log("发送消息：" + message);
        socket.send(message);
        document.getElementById("messages").innerHTML += "<p>发送消息：" + message + "</p>";
    }
</script>

</body>
</html>
