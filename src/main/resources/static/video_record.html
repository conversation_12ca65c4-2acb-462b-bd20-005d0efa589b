<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频录制控制</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: auto;
            padding-top: 50px;
            text-align: center;
        }
        input, button {
            padding: 10px;
            margin: 10px 0;
            width: 100%;
            font-size: 16px;
        }
        .response, .notification {
            margin-top: 20px;
            font-weight: bold;
            color: green;
        }
        .error {
            color: red;
        }
    </style>
</head>
<body>

<h2>启动视频录制</h2>
<p>请输入学生ID以开始录制视频：</p>

<input type="text" id="studentId" placeholder="请输入学生ID" required>
<button onclick="startRecording()">开始录制</button>

<div id="responseMessage" class="response"></div>
<div id="notificationMessage" class="notification"></div>

<script>
    let webSocket;

    // 连接 WebSocket，监听录制完成的消息
    function connectWebSocket() {
        webSocket = new WebSocket("ws://************:8088/ws");

        webSocket.onopen = () => {
            console.log("WebSocket connection established.");
        };

        webSocket.onmessage = (event) => {
            // 收到来自服务器的消息
            const notificationMessage = document.getElementById("notificationMessage");
            notificationMessage.textContent = event.data;
        };

        webSocket.onclose = () => {
            console.log("WebSocket connection closed.");
            setTimeout(connectWebSocket, 5000);  // 尝试重新连接
        };

        webSocket.onerror = (error) => {
            console.error("WebSocket error:", error);
        };
    }

    // 调用此函数来初始化 WebSocket 连接
    connectWebSocket();

    // 开始录制视频的函数
    function startRecording() {
        const studentId = document.getElementById('studentId').value;
        const responseMessage = document.getElementById('responseMessage');

        responseMessage.textContent = '';
        responseMessage.classList.remove('error');

        if (!studentId) {
            responseMessage.textContent = '请输入学生ID';
            responseMessage.classList.add('error');
            return;
        }

        fetch('http://************:8088/video/record', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({ stu_id: studentId }),
        })
        .then(response => response.text())
        .then(message => {
            responseMessage.textContent = message;
        })
        .catch(error => {
            responseMessage.textContent = '错误: ' + error.message;
            responseMessage.classList.add('error');
        });
    }
</script>

</body>
</html>
