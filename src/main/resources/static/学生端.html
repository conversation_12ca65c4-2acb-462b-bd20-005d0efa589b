<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件夹操作</title>
    <script src="https://unpkg.com/vue@3"></script>
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            font-family: Arial, sans-serif;
            background-color: #f4f4f9;
        }
        .container {
            background-color: #fff;
            padding: 20px 30px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            width: 100%;
            max-width: 400px;
            text-align: center;
        }
        h1 {
            font-size: 24px;
            color: #333;
            margin-bottom: 20px;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 16px;
        }
        .buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            justify-content: center;
        }
        .button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            color: #fff;
            cursor: pointer;
            transition: background-color 0.3s ease;
            width: 100%;
        }
        .copy-btn {
            background-color: #4CAF50;
        }
        .delete-btn {
            background-color: #F44336;
        }
        .button:hover {
            opacity: 0.9;
        }
        .message {
            margin-top: 20px;
            font-size: 16px;
            color: #333;
        }
    </style>
</head>
<body>
    <div id="app" class="container">
        <h1>文件夹操作</h1>

        <input type="text" v-model="sourcePath" placeholder="请输入源文件夹路径" />
        <input type="text" v-model="targetPath" placeholder="请输入目标文件夹路径" />

        <div class="buttons">
            <button class="button copy-btn" @click="copyDirectory">拷贝文件夹</button>
        </div>

        <input type="text" v-model="deletePath" placeholder="请输入要备份的文件夹路径" />
        <div class="buttons">
            <button class="button delete-btn" @click="deleteDirectory">备份文件夹</button>
        </div>

        <div v-if="message" class="message">{{ message }}</div>
    </div>

    <script>
        const { createApp } = Vue;

        createApp({
            data() {
                return {
                    sourcePath: '',
                    targetPath: '',
                    deletePath: '',
                    message: ''
                };
            },
            methods: {
                async copyDirectory() {
                    try {
                        const response = await fetch(
                            'http://************:8088/copy?source=' + encodeURIComponent(this.sourcePath) +
                            '&target=' + encodeURIComponent(this.targetPath),
                            { method: 'POST' }
                        );

                        if (response.ok) {
                            this.message = '文件夹拷贝成功！';
                        } else {
                            this.message = '文件夹拷贝失败！';
                        }
                    } catch (error) {
                        console.error('拷贝出错:', error);
                        this.message = '拷贝出错，请重试！';
                    }
                },
                async deleteDirectory() {
                    try {
                        const response = await fetch(
                            'http://localhost:8088/delete?target=' + encodeURIComponent(this.deletePath),
                            { method: 'POST' }
                        );

                        if (response.ok) {
                            this.message = '文件夹备份成功！';
                        } else {
                            this.message = '文件夹备份失败！';
                        }
                    } catch (error) {
                        console.error('备份出错:', error);
                        this.message = '备份出错，请重试！';
                    }
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
