<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传</title>
    <script src="https://unpkg.com/vue@3"></script> <!-- 引入 Vue 3 -->
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        .upload-container {
            text-align: center;
        }
        .file-list {
            margin-top: 20px;
            list-style: none;
            padding: 0;
        }
    </style>
</head>
<body>
    <div id="app" class="upload-container">
        <h1>文件上传</h1>
        <input type="file" multiple @change="handleFileUpload" ref="fileInput">
        <button @click="uploadFiles">上传</button>
        <div class="file-list">
            <h3>已选择的文件：</h3>
            <ul>
                <li v-for="file in files" :key="file.name">{{ file.name }}</li>
            </ul>
        </div>
    </div>
    <script>
        const { createApp } = Vue;

        createApp({
            data() {
                return {
                    files: []
                };
            },
            methods: {
                handleFileUpload(event) {
                    const selectedFiles = Array.from(event.target.files);
                    this.files = selectedFiles; // 更新文件列表
                },
                async uploadFiles() {
                    const formData = new FormData();
                    this.files.forEach(file => {
                        formData.append('files', file);
                    });

                    try {
                        const response = await fetch('http://localhost:8088/upload/teachers', {
                            method: 'POST',
                            body: formData
                        });
                        if (response.ok) {
                            alert('文件上传成功！');
                            this.files = []; // 清空文件列表
                            this.$refs.fileInput.value = ''; // 清空文件输入框
                        } else {
                            alert('文件上传失败！');
                        }
                    } catch (error) {
                        console.error('上传出错:', error);
                        alert('上传出错，请重试！');
                    }
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
